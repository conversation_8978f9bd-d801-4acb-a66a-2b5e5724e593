// Flutter imports:

import 'package:ez_intl/ez_intl.dart';
import 'package:flutter/material.dart';

// Package imports:
import 'package:auto_route/auto_route.dart';

// Project imports:
import '../../../../injector/injector.dart';
import '../../../product_confirm/widgets/base_layout.dart';
import '../../../widgets/widgets.dart';
import '../bloc/checkin_reminder_bloc.dart';
import '../widgets/checkin_reminder_view.dart';

@RoutePage()
class CheckinReminderPage extends StatefulWidget {
  const CheckinReminderPage({super.key});

  @override
  State<CheckinReminderPage> createState() => _CheckinReminderPageState();
}

class _CheckinReminderPageState extends State<CheckinReminderPage> {
  @override
  Widget build(final BuildContext context) {
    return BlocProvider(
      create: (final context) =>
          getIt<CheckinReminderBloc>()..add(CheckinReminderStarted()),
      child: BaseLayout(
        title: Text(
          context.l10n.checkinReminder,
          style: Theme.of(context).textTheme.labelLarge?.copyWith(
            fontWeight: FontWeight.w500,
            fontSize: 20,
          ),
        ),
        body: const CheckinReminderView(),
      ),
    );
  }
}
