// Flutter imports:
import 'package:flutter/material.dart';

class AccountField extends StatelessWidget {
  const AccountField({
    super.key,
    this.isOnlyReady = true,
    this.maxLines,
    required this.controller,
    required this.label,
    required this.hintText,
    this.iconRight,
    this.maxLength,
    this.onTap,
    this.onChanged,
    this.onSubmitted,
    this.bottomPadding = 8.0,
    this.radius = 8.0,
    this.filled = true,
    this.autoFocus = false,
    this.typeInput = TextInputType.text,
  });
  final bool isOnlyReady;
  final int? maxLines;
  final TextEditingController controller;
  final String label;
  final String hintText;
  final Widget? iconRight;
  final int? maxLength;
  final void Function()? onTap;
  final ValueChanged<String>? onChanged;
  final ValueChanged<String>? onSubmitted;
  final double bottomPadding;
  final double radius;
  final bool filled;
  final bool autoFocus;
  final TextInputType typeInput;

  @override
  Widget build(final BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (label.isNotEmpty)
          Column(
            children: [
              Text(label, style: const TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(height: 4.0),
            ],
          ),
        DecoratedBox(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8.0),
            border: Border.all(color: const Color(0xffDEE3ED)),
          ),
          child: Theme(
            data: Theme.of(context).copyWith(
              inputDecorationTheme: const InputDecorationTheme(
                contentPadding: EdgeInsets.all(14.0),
              ),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(radius),
              child: Padding(
                padding: (maxLines != null && !isOnlyReady)
                    ? EdgeInsets.only(bottom: bottomPadding)
                    : EdgeInsets.zero,
                child: TextField(
                  keyboardType: typeInput,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    decorationColor: Colors.white.withValues(alpha: 0),
                  ),
                  controller: controller,
                  maxLines: maxLines,
                  readOnly: isOnlyReady,
                  maxLength: maxLength,
                  onTap: onTap,
                  onChanged: onChanged,
                  autofocus: autoFocus,
                  decoration: InputDecoration(
                    suffixIcon: iconRight,
                    border: InputBorder.none,
                    enabledBorder: InputBorder.none,
                    focusedBorder: InputBorder.none,
                    errorBorder: InputBorder.none,
                    hintText: hintText,
                    hintStyle: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Theme.of(context).hintColor,
                    ),
                    counterText: isOnlyReady ? '' : null,
                    counterStyle: TextStyle(
                      color: Colors.grey.withValues(alpha: .9),
                    ),
                    filled: filled,
                    fillColor: Colors.white,
                  ),
                  onSubmitted: onSubmitted,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
