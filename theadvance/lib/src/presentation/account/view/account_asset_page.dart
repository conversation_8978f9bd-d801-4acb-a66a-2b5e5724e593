// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:auto_route/auto_route.dart';

// Project imports:
import '../../../data/models/nd_models.dart';
import '../../product_confirm/widgets/base_layout.dart';
import '../widgets/account_asset_body.dart';

@RoutePage()
class AccountAssetPage extends StatelessWidget {
  const AccountAssetPage({super.key, this.asset});
  final ProfileAssetModel? asset;
  @override
  Widget build(final BuildContext context) {
    return BaseLayout(
      body: AccountAssetBody(items: asset?.data),
      title: Text(
        asset?.title ?? '',
        style: Theme.of(context).textTheme.labelLarge?.copyWith(
          fontWeight: FontWeight.w500,
          fontSize: 18,
        ),
      ),
    );
  }
}
