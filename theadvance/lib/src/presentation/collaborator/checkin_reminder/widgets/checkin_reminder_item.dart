// Flutter imports:
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

Column checkinReminderItem(
  final String title,
  final int index,
  final Function({required bool value, required int index}) onChange,
  final BuildContext context, {
  required final bool isSwitched,
}) {
  return Column(
    children: [
      Row(
        children: [
          Expanded(
            child: Text(
              title,
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                color: Theme.of(context).primaryColorDark,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          CupertinoSwitch(
            value: isSwitched,
            onChanged: (final value) {
              onChange(value: value, index: index);
            },
            inactiveTrackColor: Theme.of(
              context,
            ).primaryColor.withValues(alpha: 0.5),
            activeTrackColor: Theme.of(context).primaryColor,
          ),
        ],
      ),
    ],
  );
}
