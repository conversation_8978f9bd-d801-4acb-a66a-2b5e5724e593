// Dart imports:
import 'dart:async';

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:ez_core/ez_core.dart';
import 'package:injectable/injectable.dart';

// Project imports:
import '../../../../core/params/request_params.dart';
import '../../../../core/params/update_bio_request_params.dart';
import '../../../../data/datasources/ez_datasources.dart';
import '../../../../data/models/nd_models.dart';
import '../../../../domain/usecases/home/<USER>';
import '../../../../domain/usecases/media/upload_avatar_usecase.dart';
import '../../../../domain/usecases/media/upload_background_usecase.dart';
import '../../../../domain/usecases/user/get_profile_usecase.dart';
import '../../../../domain/usecases/user/update_bio_usecase.dart';

// Project imports:

part 'profile_event.dart';
part 'profile_state.dart';

@injectable
class ProfileBloc extends Bloc<ProfileEvent, ProfileState> {
  ProfileBloc(
    this._getCollaboratorProfileUseCase,
    this._uploadAvatarUseCase,
    this._updateBioUseCase,
    this._uploadBackgroundUseCase,
    this._getCollaboratorServicesUseCase,
  ) : super(ProfileInitial()) {
    on<ProfileHomeFetched>(
      (final event, final emit) async => handleServicePageFetch(emit),
    );
    on<ProfileLoaded>(_handleProfileLoad);
    on<ProfileAvatarUploaded>(handleUploadAvatar);
    on<ProfileBackgroundUploaded>(handleUploadBackground);
    on<ProfileUpdateBio>(handleProfileUpdateBio);
  }

  final GetServicesUseCase _getCollaboratorServicesUseCase;
  final GetProfileUseCase _getCollaboratorProfileUseCase;
  final UploadAvatarUseCase _uploadAvatarUseCase;
  final UploadBackgroundUseCase _uploadBackgroundUseCase;
  final UpdateBioUseCase _updateBioUseCase;
  ProfileItemsModel? itemsProfile;

  Future<void> handleServicePageFetch(final Emitter<ProfileState> emit) async {
    try {
      emit(ProfileInProgress());
      final dataState = await _getCollaboratorServicesUseCase();
      if (dataState is DataSuccess) {
        final res = dataState.data;
        if (res?.errorCode == ErrorCodes.success) {
          emit(
            ProfileHomeFetchSuccess(
              services: res?.data,
              cachedServices: await EZCache.shared.collaboratorServices,
              checkListHomeMenu: await EZCache.shared.checkListHomeMenu,
            ),
          );
        } else {
          emit(
            ProfileLoadFailure(
              apiError: ApiError(
                message: res?.errorMessage,
                code: res?.errorCode,
              ),
            ),
          );
        }
      }
      if (dataState is DataFailure) {
        emit(
          ProfileLoadFailure(
            apiError: ApiError(
              message: dataState.error?.message,
              code: dataState.error?.code,
            ),
          ),
        );
      }
    } catch (error) {
      emit(ProfileLoadFailure());
    }
  }

  Future<void> _handleProfileLoad(
    final ProfileLoaded event,
    final Emitter<ProfileState> emit,
  ) async {
    try {
      emit(ProfileInProgress());
      final dataState = await _getCollaboratorProfileUseCase();
      if (dataState is DataSuccess) {
        final res = dataState.data;
        if (res?.code == ErrorCodes.success && res?.data != null) {
          await EZCache.shared.saveUserProfile(
            res?.data?.items?.userBundle?.user,
          );
          itemsProfile = res?.data?.items;
          emit(ProfileLoadSuccess(itemsProfile: res?.data?.items));
        } else {
          emit(
            ProfileLoadFailure(
              apiError: ApiError(code: res?.code, message: res?.message),
            ),
          );
        }
      }
      if (dataState is DataFailure) {
        emit(ProfileLoadFailure(apiError: dataState.error));
      }
    } catch (error) {
      emit(ProfileLoadFailure());
    }
  }

  FutureOr<void> handleProfileUpdateBio(
    final ProfileUpdateBio event,
    final Emitter<ProfileState> emit,
  ) async {
    try {
      emit(ProfileInProgress());
      final dataState = await _updateBioUseCase(params: event.params);
      if (dataState is DataSuccess) {
        emit(
          ProfileUpdateBioSuccess(
            itemsProfile: dataState.data,
            updateType: event.updateType,
          ),
        );
      }

      if (dataState is DataFailure) {
        emit(ProfileLoadFailure(apiError: dataState.error));
      }
    } catch (error) {
      emit(ProfileLoadFailure());
    }
  }

  Future<void> handleUploadAvatar(
    final ProfileAvatarUploaded event,
    final Emitter<ProfileState> emit,
  ) async {
    try {
      emit(ProfileInProgress());
      final uploadDataState = await _uploadAvatarUseCase(
        params: MediaUploadRequestParams(
          MultipartFile.fromFileSync(event.path),
        ),
      );
      if (uploadDataState is DataSuccess) {
        final upload = uploadDataState.data;
        final dataState = await _updateBioUseCase(
          params: UpdateBioRequestParams(avatar: upload?.link),
        );
        if (dataState is DataSuccess) {
          final response = dataState.data;
          if (response?.errorCode == ErrorCodes.success) {
            await EZCache.shared.saveUserProfile(response?.data);
            itemsProfile?.userBundle?.user.avatar =
                response?.data?.avatar ?? '';
            emit(ProfileUpdateSuccess(user: response?.data ?? UserModel()));
            emit(ProfileLoadSuccess(itemsProfile: itemsProfile));
          } else {
            emit(
              ProfileUpdateFailure(
                apiError: ApiError(
                  code: response?.errorCode,
                  message: response?.errorMessage,
                ),
              ),
            );
          }
        }
        if (dataState is DataFailure) {
          emit(ProfileUpdateFailure(apiError: dataState.error));
        }
      }
      if (uploadDataState is DataFailure) {
        emit(ProfileUpdateFailure(apiError: uploadDataState.error));
      }
    } catch (_) {
      emit(ProfileUpdateFailure());
    }
  }

  Future<void> handleUploadBackground(
    final ProfileBackgroundUploaded event,
    final Emitter<ProfileState> emit,
  ) async {
    try {
      emit(ProfileInProgress());
      final uploadDataState = await _uploadBackgroundUseCase(
        params: MediaUploadRequestParams(
          MultipartFile.fromFileSync(event.path),
        ),
      );
      if (uploadDataState is DataSuccess) {
        final upload = uploadDataState.data;
        final dataState = await _updateBioUseCase(
          params: UpdateBioRequestParams(coverImage: upload?.link),
        );
        if (dataState is DataSuccess) {
          final response = dataState.data;
          if (response?.errorCode == ErrorCodes.success) {
            await EZCache.shared.saveUserProfile(response?.data);
            itemsProfile?.userBundle?.user.coverImage =
                response?.data?.coverImage;
            emit(ProfileUpdateSuccess(user: response?.data ?? UserModel()));
            emit(ProfileLoadSuccess(itemsProfile: itemsProfile));
          } else {
            emit(
              ProfileUpdateFailure(
                apiError: ApiError(
                  code: response?.errorCode,
                  message: response?.errorMessage,
                ),
              ),
            );
          }
        }
        if (dataState is DataFailure) {
          emit(ProfileUpdateFailure(apiError: dataState.error));
        }
      }
      if (uploadDataState is DataFailure) {
        emit(ProfileUpdateFailure(apiError: uploadDataState.error));
      }
    } catch (_) {
      emit(ProfileUpdateFailure());
    }
  }

  Future<void> ifDataSuccess(
    final DataState<UpdateProfileResponseModel?> dataState,
    final Emitter<ProfileState> emit,
  ) async {
    if (dataState is DataSuccess) {
      final response = dataState.data;
      if (response?.errorCode == ErrorCodes.success) {
        await EZCache.shared.saveUserProfile(response?.data);
        itemsProfile?.userBundle?.user.avatar = response?.data?.avatar;
        if (response?.data != null) {
          emit(ProfileUpdateSuccess(user: response!.data!));
        }
        emit(ProfileLoadSuccess(itemsProfile: itemsProfile));
      } else {
        emit(
          ProfileUpdateFailure(
            apiError: ApiError(
              code: response?.errorCode,
              message: response?.errorMessage,
            ),
          ),
        );
      }
    }
  }
}
