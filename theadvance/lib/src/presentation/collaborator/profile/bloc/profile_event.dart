part of 'profile_bloc.dart';

enum UpdateType { all, nickName, bio }

@immutable
abstract class ProfileEvent extends Equatable {
  @override
  List<Object?> get props => [];
}

class ProfileHomeFetched extends ProfileEvent {}

class ProfileLoaded extends ProfileEvent {}

class ProfileUpdateBio extends ProfileEvent {
  ProfileUpdateBio(this.params, {this.updateType = UpdateType.all});

  final UpdateBioRequestParams params;
  final UpdateType updateType;
}

class ProfileAvatarUploaded extends ProfileEvent {
  ProfileAvatarUploaded({required this.path});

  final String path;

  @override
  List<Object?> get props => [path];
}

class ProfileBackgroundUploaded extends ProfileEvent {
  ProfileBackgroundUploaded({required this.path});

  final String path;

  @override
  List<Object?> get props => [path];
}
