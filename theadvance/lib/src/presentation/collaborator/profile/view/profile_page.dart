// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:auto_route/auto_route.dart';
import 'package:ez_core/ez_core.dart';

// Project imports:
import '../../../../data/models/nd_models.dart';
import '../../../../injector/injector.dart';
import '../../../product_confirm/widgets/base_layout.dart';
import '../bloc/profile_bloc.dart';
import '../widgets/profile_view.dart';

@RoutePage()
class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key, this.info});
  final UserBundleModel? info;
  @override
  ProfilePageState createState() => ProfilePageState();
}

class ProfilePageState extends State<ProfilePage> {
  final ValueNotifier<bool> isEdit = ValueNotifier<bool>(false);

  @override
  Widget build(final BuildContext context) {
    return BlocProvider(
      create: (final context) => getIt<ProfileBloc>()..add(ProfileLoaded()),
      child: BaseLayout(
        title: Text(
          widget.info?.title ?? '',
          style: Theme.of(context).textTheme.labelLarge?.copyWith(
            fontWeight: FontWeight.w500,
            fontSize: 20,
          ),
        ),
        body: ProfileView(user: widget.info?.user, isEdit: isEdit),
      ),
    );
  }
}
