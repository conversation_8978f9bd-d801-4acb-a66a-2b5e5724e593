// Dart imports:

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:auto_route/auto_route.dart';
import 'package:ez_core/ez_core.dart';
import 'package:ez_intl/ez_intl.dart';
import 'package:ez_resources/ez_resources.dart';

// Project imports:
import '../../../core/nd_constants/nd_constant.dart';
import '../../../core/params/request_params.dart';
import '../../../core/routes/app_router.dart';
import '../../../core/utils/api_error_dialog.dart';
import '../../../core/utils/nd_utils.dart';
import '../../../data/models/nd_models.dart';
import '../../_blocs/collaborator_user/collaborator_user_bloc.dart';
import '../../collaborator/collaborator.dart';
import 'account_avatar.dart';

class AccountBody extends StatefulWidget {
  const AccountBody({final Key? key}) : super(key: key);
  static ValueNotifier<UserModel?> user = ValueNotifier(null);
  @override
  State<AccountBody> createState() => _AccountBodyState();
}

class _AccountBodyState extends State<AccountBody> {
  ProfileItemsModel? data;

  @override
  Widget build(final BuildContext context) {
    return BlocConsumer<ProfileBloc, ProfileState>(
      listener: (final context, final state) async {
        if (state is ProfileHomeFetchSuccess) {
          HomeBody.services.setValue(state.services?.menuItems ?? []);
        }
        if (state is ProfileUpdateFailure) {
          Alert.showAlert(
            AlertParams(
              context,
              state.apiError?.message,
              acceptButton: context.l10n.close,
            ),
          );
        }
        if (state is ProfileLoadFailure) {
          ApiErrorDialog.show(
            ApiErrorParams(
              context,
              state.apiError,
              acceptButtonTitle: context.l10n.accept,
            ),
          );
        }

        if (state is ProfileUpdateSuccess) {
          context.read<CollaboratorUserBloc>().add(
            CollaboratorUserSynced(state.user),
          );
        }
        if (state is ProfileLoadSuccess) {
          data = state.itemsProfile;
          AccountBody.user.value = data?.userBundle?.user;
        }
      },
      builder: (final context, final state) {
        return SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              spacing: 16,
              children: [
                DecoratedBox(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: _buildAvatar(),
                ),
                DecoratedBox(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: SizedBox(
                    child: Column(
                      children: [
                        const SizedBox(height: 12),
                        buildItem(
                          iconPath: AppIcons.icProfileInfo,
                          title: context.l10n.personInfo,
                          router: ProfileRoute(info: data?.userBundle),
                          isLast: true,
                        ),
                      ],
                    ),
                  ),
                ),
                DecoratedBox(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: SizedBox(
                    child: Column(
                      children: [
                        const SizedBox(height: 12),
                        buildItem(
                          iconPath: AppIcons.icAsset,
                          title: context.l10n.asset,
                          router: AccountAssetRoute(asset: data?.asset),
                        ),
                        buildItem(
                          iconPath: AppIcons.icSocialInsurance,
                          title: context.l10n.socialInsurance,
                          router: AccountPreviewRoute(
                            asset: data?.socialInsurance,
                          ),
                        ),
                        buildItem(
                          iconPath: AppIcons.icWorkContract,
                          title: context.l10n.laborContract,
                          router: AccountPreviewRoute(
                            asset: data?.workingContract,
                          ),
                          isLast: true,
                        ),
                      ],
                    ),
                  ),
                ),
                DecoratedBox(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    children: [
                      const SizedBox(height: 12),
                      buildItem(
                        iconPath: AppIcons.icMainScreen,
                        title: context.l10n.mainScreen,
                        router: const EditHomeMenuRoute(),
                      ),
                      buildItem(
                        iconPath: AppIcons.icCheckinReminder,
                        title: context.l10n.checkinReminder,
                        router: const CheckinReminderRoute(),
                      ),
                      buildItem(
                        iconPath: AppIcons.icSetting,
                        title: context.l10n.settings,
                        router: SettingRoute(),
                        isLast: true,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget buildItem({
    final String? iconPath,
    final bool isLast = false,
    final String title = Strings.empty,
    final PageRouteInfo<dynamic>? router,
  }) {
    return GestureDetector(
      onTap: () async {
        if (router != null) {
          context.router.push(router);
        }
      },
      child: SizedBox(
        width: double.maxFinite,
        child: Padding(
          padding: const EdgeInsets.only(left: 16, bottom: 16),
          child: Column(
            spacing: 12,
            children: [
              Row(
                spacing: 8,
                children: [
                  if (iconPath != null)
                    EZResources.image(ImageParams(name: iconPath)),
                  Expanded(
                    child: Column(
                      spacing: 12,
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 8),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(title),
                              EZResources.image(
                                ImageParams(
                                  name: AppIcons.icForward,
                                  size: const ImageSize.square(24),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              if (!isLast)
                const Padding(
                  padding: EdgeInsets.only(left: 40),
                  child: Divider(thickness: .35),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAvatar() {
    return SizedBox(
      width: double.maxFinite,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          spacing: 8,
          children: [
            ValueListenableBuilder(
              valueListenable: AccountBody.user,
              builder: (final context, final vUser, final child) {
                return AccountAvatar(
                  size: 48,
                  tag: vUser?.employeeId ?? '',
                  url: vUser?.avatar ?? Strings.empty,
                );
              },
            ),
            Expanded(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  ValueListenableBuilder(
                    valueListenable: AccountBody.user,
                    builder: (final context, final vUser, final child) {
                      return GestureDetector(
                        onTap: () async => context.router.push(
                          StoryPersonRoute(
                            codeUser: vUser?.employeeId ?? Strings.empty,
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              vUser?.name ?? Strings.empty,
                              textAlign: TextAlign.center,
                              style: Theme.of(
                                context,
                              ).textTheme.labelLarge?.copyWith(fontSize: 18),
                            ),
                            Text(
                              context.l10n.personInfo,
                              style: Theme.of(context).textTheme.bodySmall
                                  ?.copyWith(color: Colors.black54),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                  EZResources.image(
                    ImageParams(
                      name: AppIcons.icForward,
                      size: const ImageSize.square(24),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
