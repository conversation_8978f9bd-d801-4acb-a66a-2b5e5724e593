import 'package:flutter/material.dart';

class CheckinReminderNumberPicker extends StatefulWidget {
  const CheckinReminderNumberPicker({
    final Key? key,
    this.min = 0,
    this.max = 100,
    this.initialValue = 50,
    this.step = 1,
    this.onChanged,
    this.label,
    this.unit,
  }) : super(key: key);
  final int min;
  final int max;
  final int initialValue;
  final int step;
  final Function(int)? onChanged;
  final String? label;
  final String? unit;

  @override
  State<CheckinReminderNumberPicker> createState() =>
      _CheckinReminderNumberPickerState();
}

class _CheckinReminderNumberPickerState
    extends State<CheckinReminderNumberPicker> {
  late FixedExtentScrollController _scrollController;
  late int _selectedValue;
  final double _itemHeight = 50.0;
  final int _paddingItems = 2;

  late List<int> _numbers;

  @override
  void initState() {
    super.initState();
    _selectedValue = widget.initialValue;
    _numbers = <int>[];
    for (int i = widget.min; i <= widget.max; i += widget.step) {
      _numbers.add(i);
    }
    final int initialIndexInNumbers = _numbers.indexOf(widget.initialValue);
    final int initialItemForController = initialIndexInNumbers + _paddingItems;

    _scrollController = FixedExtentScrollController(
      initialItem: initialItemForController,
    );
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }
  Widget _buildNumberItem(final int? number, final bool isSelected) {
    return SizedBox(
      height: _itemHeight,
      child: AnimatedDefaultTextStyle(
        duration: const Duration(milliseconds: 200),
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          fontSize: isSelected ? 18 : 16,
          fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
          color: number == null
              ? Colors
                    .transparent 
              : isSelected
              ? Colors.black
              : Colors.black54,
        )
        ??const TextStyle(),
        child: Transform.scale(
          scale: isSelected ? 1.1 : 1.0,
          child: Center(child: Text(number.toString().padLeft(2, '0'))),
        ),
      ),
    );
  }

  @override
  Widget build(final BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(20),
      ),
      child: ListWheelScrollView(
        itemExtent: 50,
        controller: _scrollController,
        physics: const FixedExtentScrollPhysics(),
        itemExtent: _itemHeight,
        onSelectedItemChanged: (final int index) {
          final int actualNumberIndex = index - _paddingItems;
          if (actualNumberIndex >= 0 &&
              actualNumberIndex < _numbers.length) {
            final int newValue = _numbers[actualNumberIndex];
            if (newValue != _selectedValue) {
              setState(() {
                _selectedValue = newValue;
              });
              widget.onChanged?.call(newValue);
            }
          }
        },
        children: <Widget>[
          for (int i = 0; i < _paddingItems; i++)
            _buildNumberItem(null, false),
          for (final int number in _numbers)
            _buildNumberItem(number, number == _selectedValue),
          for (int i = 0; i < _paddingItems; i++)
            _buildNumberItem(null, false),
        ],
      ),
    );
  }
}
