// Flutter imports:
import 'package:ez_intl/ez_intl.dart';
import 'package:flutter/material.dart';

// Project imports:
import '../../../data/models/nd_models.dart';

class AccountPreviewBody extends StatelessWidget {
  const AccountPreviewBody({super.key, this.items});
  final List<ProfileInfoModel>? items;
  @override
  Widget build(final BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: DecoratedBox(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            //  spacing: 10,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ...List.generate(items?.length ?? 0, (final i) {
                return _buildInfoField(
                  context,
                  items?[i].title ?? '',
                  items?[i].content ?? '',
                  i == (items?.length ?? 0) - 1,
                );
              }),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoField(
    final BuildContext context,
    final String title,
    final String value, [
    final bool isEndField = false,
  ]) {
    return Padding(
      padding: const EdgeInsets.only(left: 16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 16),
          Padding(
            padding: const EdgeInsets.only(right: 16),
            child: Row(
              spacing: 16,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title,
                  style: Theme.of(
                    context,
                  ).textTheme.labelLarge?.copyWith(fontSize: 15),
                ),
                if (value.isNotEmpty)
                  Expanded(
                    child: Align(
                      alignment: Alignment.centerRight,
                      child: Text(
                        value,
                        textAlign: TextAlign.start,
                        maxLines: 2,
                        style: Theme.of(context).textTheme.labelLarge?.copyWith(
                          fontSize: 15,
                          color: Colors.black45,
                        ),
                      ),
                    ),
                  )
                else
                  Expanded(
                    child: Align(
                      alignment: Alignment.centerRight,
                      child: Text(
                        context.l10n.notUpdated,
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          fontStyle: FontStyle.italic,
                          color: Colors.black54,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          if (!isEndField) const Divider(thickness: .35),
        ],
      ),
    );
  }
}
