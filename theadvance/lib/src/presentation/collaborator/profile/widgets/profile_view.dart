// Dart imports:
import 'dart:async';

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:app_ui/app_ui.dart';
import 'package:auto_route/auto_route.dart';
import 'package:ez_intl/ez_intl.dart';
import 'package:ez_resources/ez_resources.dart';

// Project imports:
import '../../../../core/nd_progresshud/nd_progresshud.dart';
import '../../../../core/params/request_params.dart';
import '../../../../core/params/story_write_info_request_params.dart';
import '../../../../core/params/update_bio_request_params.dart';
import '../../../../core/routes/app_router.dart';
import '../../../../core/routes/routes.dart';
import '../../../../core/utils/nd_utils.dart';
import '../../../../data/models/nd_models.dart';
import '../../../_blocs/collaborator_user/collaborator_user_bloc.dart';
import '../../../account/widgets/account_avatar.dart';
import '../../../account/widgets/account_body.dart';
import '../../../story_list/view/story_image_detail_page.dart';
import '../../../story_list/widgets/story_image.dart';
import '../../../widgets/widgets.dart';
import '../../collaborator.dart';

class ProfileView extends StatefulWidget {
  const ProfileView({final Key? key, this.user, required this.isEdit})
    : super(key: key);
  final UserModel? user;
  final ValueNotifier<bool> isEdit;
  @override
  ProfileViewState createState() => ProfileViewState();
}

class ProfileViewState extends State<ProfileView> {
  late TextEditingController controller;
  late TextEditingController controllerNickName;
  final ScrollController _scrollController = ScrollController();
  final ValueNotifier<String> imageUpdated = ValueNotifier('');
  final ValueNotifier<bool> isEditAvatar = ValueNotifier<bool>(false);
  final ValueNotifier<bool> isEditBio = ValueNotifier<bool>(false);
  final ValueNotifier<bool> isEditNickName = ValueNotifier<bool>(false);
  final ValueNotifier<String> nickName = ValueNotifier('');
  final ValueNotifier<String> bio = ValueNotifier('');
  @override
  void initState() {
    widget.isEdit.addListener(() {
      isEditBio.value = widget.isEdit.value;
      isEditNickName.value = widget.isEdit.value;
      isEditAvatar.value = widget.isEdit.value;
      if (widget.isEdit.value) {
        unawaited(
          _scrollController.animateTo(
            0,
            duration: const Duration(milliseconds: 800),
            curve: Curves.fastEaseInToSlowEaseOut,
          ),
        );
      }
    });
    super.initState();
  }

  final double widthAvatar = 128;
  final double heightAvatar = 128;
  @override
  Widget build(final BuildContext context) {
    return BlocConsumer<ProfileBloc, ProfileState>(
      listener: (final context, final state) async {
        if (state is ProfileUpdateFailure) {
          Alert.showAlert(
            AlertParams(
              context,
              state.apiError?.message,
              acceptButton: context.l10n.close,
            ),
          );
        }
        if (state is ProfileLoadFailure) {
          Alert.popup(
            context,
            ResultPopupScreen(
              code: state.apiError?.code,
              title: state.apiError?.message,
              isSuccess: false,
            ),
          );
        }
        if (state is ProfileLoadSuccess) {
          final user = state.itemsProfile?.userBundle?.user;
          nickName.value = user?.nickName ?? '';
          bio.value = user?.bio ?? '';
        }
        if (state is ProfileUpdateSuccess) {
          context.read<CollaboratorUserBloc>().add(
            CollaboratorUserSynced(state.user),
          );
        }
        if (state is ProfileUpdateBioSuccess) {
          if (state.updateType == UpdateType.all) {
            unawaited(
              Alert.showAlertCollaborator(
                context,
                image: EZResources.image(
                  ImageParams(
                    name: AppIcons.icGreenSuccess,
                    size: ImageSize.square(
                      MediaQuery.sizeOf(context).width / 2.8,
                    ),
                  ),
                ),
                message: context.l10n.updateSuccess,
              ),
            );
            AccountBody.user.value = state.itemsProfile?.data;
          }
          if (state.updateType == UpdateType.nickName) {
            unawaited(
              Alert.showAlertCollaborator(
                context,
                image: EZResources.image(
                  ImageParams(
                    name: AppIcons.icGreenSuccess,
                    size: ImageSize.square(
                      MediaQuery.sizeOf(context).width / 2.8,
                    ),
                  ),
                ),
                message: context.l10n.updateSuccess,
              ),
            );
            nickName.value = state.itemsProfile?.data?.nickName ?? '';
          }
          if (state.updateType == UpdateType.bio) {
            unawaited(
              Alert.showAlertCollaborator(
                context,
                image: EZResources.image(
                  ImageParams(
                    name: AppIcons.icGreenSuccess,
                    size: ImageSize.square(
                      MediaQuery.sizeOf(context).width / 2.8,
                    ),
                  ),
                ),
                message: context.l10n.updateSuccess,
              ),
            );
            bio.value = state.itemsProfile?.data?.bio ?? '';
          }
        }
      },
      builder: (final context, final state) {
        return GestureDetector(
          onTap: () {
            widget.isEdit.value = false;
            isEditBio.value = false;
            isEditNickName.value = false;
            isEditAvatar.value = false;
            FocusScope.of(context).unfocus();
          },
          child: Stack(
            children: [
              SingleChildScrollView(
                controller: _scrollController,
                child: Padding(
                  padding: const EdgeInsets.only(
                    top: 16,
                    left: 16,
                    right: 16,
                    bottom: 60,
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      ValueListenableBuilder(
                        valueListenable: isEditAvatar,
                        builder:
                            (final context, final vIsEditAvatar, final child) {
                              return _buildAvatar(vIsEditAvatar);
                            },
                      ),
                      const SizedBox(height: 24),
                      Stack(
                        children: [
                          Column(
                            spacing: 12,
                            children: [
                              DecoratedBox(
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [_buildNickName(), _buildBio()],
                                ),
                              ),
                              DecoratedBox(
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Column(
                                  //  spacing: 10,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    _buildInfoField(
                                      context.l10n.name,
                                      widget.user?.name ?? '',
                                    ),
                                    _buildInfoField(
                                      context.l10n.phoneNumber,
                                      widget.user?.phone ?? '',
                                    ),
                                    _buildInfoField(
                                      context.l10n.indentify,
                                      widget.user?.identifyCard ?? '',
                                    ),
                                    _buildInfoField(
                                      context.l10n.dob,
                                      widget.user?.dob ?? '',
                                    ),
                                  ],
                                ),
                              ),
                              DecoratedBox(
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Column(
                                  //  spacing: 10,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    _buildInfoField(
                                      context.l10n.gender,
                                      widget.user?.gender ?? '',
                                    ),
                                    _buildInfoField(
                                      context.l10n.addAddress,
                                      widget.user?.address ?? '',
                                    ),
                                    _buildInfoField(
                                      context.l10n.numberBank,
                                      widget.user?.bankAccountId ?? '',
                                      false,
                                      true,
                                    ),
                                  ],
                                ),
                              ),
                              DecoratedBox(
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Column(
                                  //  spacing: 10,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    _buildInfoField(
                                      context.l10n.staffCode,
                                      widget.user?.employeeId ?? '',
                                    ),
                                    _buildInfoField(
                                      context.l10n.branch,
                                      widget.user?.branchName ?? '',
                                    ),
                                    _buildInfoField(
                                      context.l10n.positionMember,
                                      widget.user?.jobTitleName ?? '',
                                      false,
                                      true,
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          ValueListenableBuilder(
                            valueListenable: widget.isEdit,
                            builder: (final context, final vEdit, final child) {
                              return vEdit
                                  ? Positioned.fill(
                                      child: ColoredBox(
                                        color: const Color(
                                          0xffF6F7FB,
                                        ).withValues(alpha: .6),
                                      ),
                                    )
                                  : const SizedBox();
                            },
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              ValueListenableBuilder(
                valueListenable: widget.isEdit,
                builder: (final context, final vIsEdit, final child) {
                  return vIsEdit
                      ? Align(
                          alignment: Alignment.bottomCenter,
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: BaseAppButton(
                              onPressed: () {
                                final params = UpdateBioRequestParams(
                                  bio: controller.text.trim(),
                                  nickname: controllerNickName.text.trim(),
                                );
                                context.read<ProfileBloc>().add(
                                  ProfileUpdateBio(params),
                                );
                              },
                              title: context.l10n.save,
                            ),
                          ),
                        )
                      : const SizedBox();
                },
              ),
              if (state is ProfileInProgress)
                const Center(child: LoadingWidget()),
            ],
          ),
        );
      },
    );
  }

  ValueListenableBuilder<String> _buildNickName() {
    return ValueListenableBuilder(
      valueListenable: nickName,
      builder: (final context, final vNickName, final child) {
        return GestureDetector(
          onTap: () {
            context.router
                .push(
                  ProfileDetailRoute(
                    title: context.l10n.nickName,
                    value: vNickName,
                  ),
                )
                .then((final val) {
                  if (val != null && val is String) {
                    nickName.value = val;
                    final params = UpdateBioRequestParams(
                      bio: bio.value,
                      nickname: val,
                    );
                    if (context.mounted) {
                      context.read<ProfileBloc>().add(
                        ProfileUpdateBio(
                          params,
                          updateType: UpdateType.nickName,
                        ),
                      );
                    }
                  }
                });
          },
          child: _buildInfoField(context.l10n.nickName, vNickName, true),
        );
      },
    );
  }

  ValueListenableBuilder<String> _buildBio() {
    return ValueListenableBuilder(
      valueListenable: bio,
      builder: (final context, final vBio, final child) {
        return GestureDetector(
          onTap: () {
            context.router
                .push(
                  ProfileDetailRoute(
                    title: context.l10n.bio,
                    value: vBio,
                    isLines: true,
                  ),
                )
                .then((final val) {
                  if (val != null && val is String) {
                    bio.value = val;
                    final params = UpdateBioRequestParams(
                      bio: val,
                      nickname: nickName.value,
                    );
                    if (context.mounted) {
                      context.read<ProfileBloc>().add(
                        ProfileUpdateBio(params, updateType: UpdateType.bio),
                      );
                    }
                  }
                });
          },
          child: _buildInfoField(context.l10n.bio, vBio, true, true),
        );
      },
    );
  }

  Stack _buildAvatar(final bool vIsEdit) {
    return Stack(
      children: [
        ValueListenableBuilder(
          valueListenable: AccountBody.user,
          builder: (final context, final vUser, final child) {
            return GestureDetector(
              onTap: () async {
                showModalBottomSheet(
                  context: context,
                  builder: (final contextBottomSheet) => ClipRRect(
                    borderRadius: BorderRadius.circular(8.0),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        ListTile(
                          title: Text(context.l10n.watchImage),
                          onTap: () async {
                            Navigator.of(context).pop();
                            StoryImageDetailPage.listMediaPreview.setValue([]);
                            StoryImageDetailPage.listMedia.setValue([
                              Material(
                                color: Colors.white.withValues(alpha: 0),
                                child: Center(
                                  child: StoryImage(
                                    originalWidth: 1,
                                    originalHeight: 1,
                                    tags: '${vUser?.employeeId}',
                                    imageUrl: vUser?.avatar ?? '',
                                    fit: BoxFit.contain,
                                    disableGestures: null,
                                    initialScale:
                                        PhotoViewComputedScale.contained,
                                    minScale: PhotoViewComputedScale.contained,
                                    maxScale:
                                        PhotoViewComputedScale.contained * 1.1,
                                  ),
                                ),
                              ),
                            ]);
                            Navigator.push(
                              context,
                              PageRouteBuilder(
                                opaque: false,
                                pageBuilder: (final _, final __, final ___) =>
                                    StoryImageDetailPage(
                                      tags: [vUser?.employeeId ?? ''],
                                      file: Attachment(
                                        link: vUser?.avatar ?? '',
                                        originalname:
                                            vUser?.avatar
                                                ?.split('/')
                                                .lastOrNull ??
                                            '',
                                      ),
                                      initIndex: 0,
                                      url: [vUser?.avatar ?? ''],
                                      fileName: const [''],
                                    ),
                              ),
                            );
                          },
                        ),
                        ListTile(
                          title: Text(context.l10n.updateAvatar),
                          onTap: () async {
                            Navigator.of(context).pop();
                            context.router.pushNamed(Routes.gallery);
                          },
                        ),
                      ],
                    ),
                  ),
                );
              },
              child: AccountAvatar(
                size: widthAvatar,
                tag: vUser?.employeeId ?? '',
                url: vUser?.avatar ?? '',
              ),
            );
          },
        ),
        if (vIsEdit)
          Positioned(
            top: heightAvatar - 38,
            left: widthAvatar - 38,
            right: 0,
            bottom: 0,
            child: InkWell(
              onTap: () async {
                context.router.pushNamed(Routes.gallery);
              },
              child: ClipRRect(
                borderRadius: BorderRadius.circular(24),
                child: ColoredBox(
                  color: Colors.white.withValues(alpha: .9),
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: EZResources.image(
                      ImageParams(name: AppIcons.icEdit),
                    ),
                  ),
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildInfoField(
    final String title,
    final String value, [
    final bool isForward = false,
    final bool isEndField = false,
  ]) {
    return Padding(
      padding: const EdgeInsets.only(left: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 16),
          Padding(
            padding: const EdgeInsets.only(right: 16),
            child: Row(
              spacing: 16,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title,
                  style: Theme.of(
                    context,
                  ).textTheme.labelLarge?.copyWith(fontSize: 15),
                ),
                if (value.isNotEmpty)
                  Expanded(
                    child: Align(
                      alignment: Alignment.centerRight,
                      child: Text(
                        value,
                        textAlign: TextAlign.start,
                        maxLines: 2,
                        style: Theme.of(context).textTheme.labelLarge?.copyWith(
                          fontSize: 15,
                          color: Colors.black45,
                        ),
                      ),
                    ),
                  )
                else
                  Expanded(
                    child: Align(
                      alignment: Alignment.centerRight,
                      child: Text(
                        context.l10n.notUpdated,
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          fontStyle: FontStyle.italic,
                          color: Colors.black54,
                        ),
                      ),
                    ),
                  ),
                if (isForward)
                  EZResources.image(
                    ImageParams(
                      name: AppIcons.icForward,
                      size: const ImageSize.square(24),
                    ),
                  ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          if (!isEndField) const Divider(thickness: .35),
        ],
      ),
    );
  }
}
