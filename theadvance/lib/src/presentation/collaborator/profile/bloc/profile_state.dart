part of 'profile_bloc.dart';

@immutable
abstract class ProfileState extends Equatable {
  @override
  List<Object?> get props => [];
}

class ProfileInitial extends ProfileState {}

class ProfileInProgress extends ProfileState {}

class ProfileLoadSuccess extends ProfileState {
  ProfileLoadSuccess({required this.itemsProfile});

  final ProfileItemsModel? itemsProfile;

  @override
  List<Object?> get props => [itemsProfile];
}

class ProfileUpdateBioSuccess extends ProfileState {
  ProfileUpdateBioSuccess({
    required this.itemsProfile,
    required this.updateType,
  });

  final GenericResponseModel<UserModel>? itemsProfile;
  final UpdateType updateType;
  @override
  List<Object?> get props => [itemsProfile, updateType];
}

class ProfileLoadFailure extends ProfileState {
  ProfileLoadFailure({this.apiError});

  final ApiError? apiError;

  @override
  List<Object?> get props => [apiError];
}

class ProfileUpdateSuccess extends ProfileState {
  ProfileUpdateSuccess({required this.user});

  final UserModel user;

  @override
  List<Object?> get props => [user];
}

class ProfileUpdateFailure extends ProfileState {
  ProfileUpdateFailure({this.apiError});

  final ApiError? apiError;

  @override
  List<Object?> get props => [apiError];

  @override
  String toString() => 'Update profile failed: $apiError';
}

class ProfileHomeFetchSuccess extends ProfileState {
  ProfileHomeFetchSuccess({
    required this.services,
    required this.checkListHomeMenu,
    required this.cachedServices,
  });

  final ServiceTabBarModel? services;
  final Map<String, bool> checkListHomeMenu;
  final List<ServiceItemsModel?> cachedServices;
}
